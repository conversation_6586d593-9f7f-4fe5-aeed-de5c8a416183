package bridge

import (
	"context"
	"errors"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreateBridgeRecord(t *testing.T) {
	ctrl := gomock.NewController(t)
	ctx := context.Background()
	params := domain.CreateBridgeRecordParams{
		OrgID:            1,
		UID:              util.Ptr("123"),
		FromChain:        domain.Arbitrum,
		FromAddress:      domain.NewEvmAddress("******************************************"),
		FromTokenAddress: "******************************************",
		FromAmount:       decimal.NewFromInt(5),
		FromTxHash:       "0xf98a8eb02ad8da1b176628552347ed2cee72f52ae8ac2ba13ce51a1003c38f36",
		// Not provided when creating bridge record
		// toTxHash := "0xceec7418a57dd078823fb1cf29ab1776e9e6f3f39143b4903a450a2fde768e9a"

		ToChain:        domain.Polygon,
		ToAddress:      domain.NewEvmAddress("******************************************"),
		ToTokenAddress: "******************************************",
		ToAmount:       decimal.NewFromFloat(4.849878),

		FeeChain:           domain.Arbitrum,
		FeeReceiveAddress:  domain.NewEvmAddress("0xcce1d3afec60d74bf18d9ec25a995a02c710ebbc"),
		FeeTokenAddress:    "******************************************",
		FeeTxHash:          "0xf98a8eb02ad8da1b176628552347ed2cee72f52ae8ac2ba13ce51a1003c38f36",
		EstimatedFeeAmount: decimal.NewFromFloat(0.15),
	}

	t.Run("Get price failed", func(t *testing.T) {
		repo := domain.NewMockBridgeRepo(ctrl)
		repo.EXPECT().GetTokenPrice(gomock.Any(), gomock.Any(), gomock.Any()).Return(0.0, errors.New("test error"))
		Init(repo)
		kgErr := CreateBridgeRecord(ctx, &params)
		assert.EqualError(t, kgErr.Error, "test error")
	})

	t.Run("Fee is not native token", func(t *testing.T) {
		repo := domain.NewMockBridgeRepo(ctrl)
		repo.EXPECT().GetTokenPrice(gomock.Any(), domain.Arbitrum, "******************************************").Times(2).Return(0.5, nil)
		metaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		tokenmeta.Init(metaRepo, nil, nil)
		repo.EXPECT().GetProfitRate(gomock.Any(), gomock.Any(), gomock.Any()).Return(&domain.AssetProProfitRate{
			Service:          domain.ProfitRateServiceTypeBridge,
			ProfitRate:       decimal.NewFromFloat(0.1),
			ProfitShareRatio: decimal.NewFromFloat(0.3),
		}, nil)
		repo.EXPECT().CreateBridgeRecord(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, bridge *domain.BridgeRecord) error {
			assert.InDelta(t, 0.15, bridge.FeeAmount.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.5, bridge.FeeTokenPrice.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.075, bridge.FeeUSD.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.003, bridge.ProfitKgMinimumRate.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.1, bridge.ProfitRate.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.3, bridge.ProfitShareRatio.InexactFloat64(), 0.0001)
			// allFee: 0.15, kgMinimumRevenue: 5 * 0.003 = 0.015
			// profitMargin: (0.15 - 0.015) * (1 - 0.3) = 0.0945
			// profitMargin(USD): 0.0945 * 0.5 = 0.04725
			assert.InDelta(t, 0.04725, bridge.ProfitMargin.InexactFloat64(), 0.0001)
			return nil
		})
		Init(repo)
		kgErr := CreateBridgeRecord(ctx, &params)
		if kgErr != nil {
			t.Fatalf("CreateBridgeRecord failed: %v", kgErr.Error)
		}
	})
}

func TestCreateDefiSwapRecord(t *testing.T) {
	ctrl := gomock.NewController(t)
	ctx := context.Background()
	params := domain.CreateBridgeRecordParams{
		OrgID:            1,
		UID:              util.Ptr("123"),
		FromChain:        domain.Arbitrum,
		FromAddress:      domain.NewEvmAddress("0x5755Cf8Ccc2ba950fF833Bb30AC0607281b113b4"),
		FromTokenAddress: "******************************************",
		FromAmount:       decimal.NewFromFloat(0.0000987),
		FromTxHash:       "0xdeb3403fc6bd887536ffe232c30765e0d3d322c2ce3ec1d05fb76f472633e80a",

		ToChain:        domain.Arbitrum,
		ToAddress:      domain.NewEvmAddress("0x5755Cf8Ccc2ba950fF833Bb30AC0607281b113b4"),
		ToTokenAddress: "******************************************",
		ToTxHash:       util.Ptr("0xdeb3403fc6bd887536ffe232c30765e0d3d322c2ce3ec1d05fb76f472633e80a"),
		ToAmount:       decimal.NewFromFloat(0.269514),

		FeeChain:           domain.Arbitrum,
		FeeReceiveAddress:  domain.NewEvmAddress("0x9bae3a9cbac6e769d980f6ce0fd52397dd45d361"),
		FeeTokenAddress:    "******************************************",
		FeeTxHash:          "0xdeb3403fc6bd887536ffe232c30765e0d3d322c2ce3ec1d05fb76f472633e80a",
		EstimatedFeeAmount: decimal.NewFromFloat(0.0000013),
	}

	t.Run("Wrong fee receiver", func(t *testing.T) {
		curParams := params
		curParams.FeeReceiveAddress = domain.NewEvmAddress("0x5755Cf8Ccc2ba950fF833Bb30AC0607281b113b4")
		repo := domain.NewMockBridgeRepo(ctrl)
		repo.EXPECT().GetTokenPrice(gomock.Any(), domain.Arbitrum, "******************************************").Times(2).Return(2000.0, nil)
		metaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		metaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{{
			Chain:   domain.Arbitrum,
			TokenID: "******************************************",
		}}).Return(map[domain.ChainToken]*domain.TokenMetadata{
			{
				Chain:   domain.Arbitrum,
				TokenID: "******************************************",
			}: {
				Decimals:    18,
				CoingeckoID: "usd-coin",
			},
		}, nil)
		tokenmeta.Init(metaRepo, nil, nil)
		repo.EXPECT().GetProfitRate(gomock.Any(), gomock.Any(), gomock.Any()).Return(&domain.AssetProProfitRate{
			Service:          domain.ProfitRateServiceTypeSwapDefi,
			ProfitRate:       decimal.NewFromFloat(0.1),
			ProfitShareRatio: decimal.NewFromFloat(0.3),
		}, nil)
		Init(repo)
		kgErr := CreateBridgeRecord(ctx, &curParams)
		assert.NotNil(t, kgErr)
		t.Logf("err: %v", kgErr.Error)
		assert.Contains(t, kgErr.Error.Error(), "profit margin is negative")
	})

	t.Run("ETH to USDT on Arbitrum", func(t *testing.T) {
		repo := domain.NewMockBridgeRepo(ctrl)
		repo.EXPECT().GetTokenPrice(gomock.Any(), domain.Arbitrum, "******************************************").Times(2).Return(2000.0, nil)
		metaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		tokenmeta.Init(metaRepo, nil, nil)
		repo.EXPECT().GetProfitRate(gomock.Any(), gomock.Any(), gomock.Any()).Return(&domain.AssetProProfitRate{
			Service:          domain.ProfitRateServiceTypeSwapDefi,
			ProfitRate:       decimal.NewFromFloat(0.1),
			ProfitShareRatio: decimal.NewFromFloat(0.3),
		}, nil)
		repo.EXPECT().CreateBridgeRecord(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, bridge *domain.BridgeRecord) error {
			assert.InDelta(t, 0.0000013, bridge.FeeAmount.InexactFloat64(), 0.0000001)
			assert.InDelta(t, 2000, bridge.FeeTokenPrice.InexactFloat64(), 0.01)
			assert.InDelta(t, 0.0026, bridge.FeeUSD.InexactFloat64(), 0.00001)
			assert.InDelta(t, 0.0015, bridge.ProfitKgMinimumRate.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.1, bridge.ProfitRate.InexactFloat64(), 0.0001)
			assert.InDelta(t, 0.3, bridge.ProfitShareRatio.InexactFloat64(), 0.0001)
			// allFee: 0.0000013, kgMinimumRevenue: 0.0000987 * 0.0015 = 1.4805e-7
			// profitMargin: (0.0000013 - 1.4805e-7) * (1 - 0.3) = 8.06365e-7
			// profitMargin(USD): 8.06365e-7 * 2000 = 0.00161273
			assert.InDelta(t, 0.00161273, bridge.ProfitMargin.InexactFloat64(), 0.0000001)
			return nil
		})
		Init(repo)
		kgErr := CreateBridgeRecord(ctx, &params)
		if kgErr != nil {
			t.Fatalf("CreateBridgeRecord failed: %v", kgErr.Error)
		}
	})
}
