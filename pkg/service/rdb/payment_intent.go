package rdb

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (r *Repo) CreatePaymentIntent(ctx context.Context, intent *domain.PaymentIntent) (*domain.PaymentIntent, error) {
	orderDataBytes, err := json.Marshal(intent.OrderData)
	if err != nil {
		return nil, err
	}
	orderData := string(orderDataBytes)
	if len(orderData) > 1000 {
		return nil, errors.New("invalid order data: data too long")
	}

	// Get payer address string if exists
	var payerAddressStr string
	if intent.PayerAddress != nil && !intent.PayerAddress.IsEmpty() {
		payerAddressStr = intent.PayerAddress.String()
	}

	var payoutTargetAddress *string
	if intent.PayoutTargetAddress != nil && !intent.PayoutTargetAddress.IsEmpty() {
		payoutTargetAddress = util.Ptr(intent.PayoutTargetAddress.String())
	}

	record := &model.PaymentIntent{
		ID:                     util.RandString(32),
		ClientID:               intent.ClientID,
		OrgID:                  intent.OrgID,
		PaymentChain:           intent.PaymentChain.ID(),
		PaymentAddress:         intent.PaymentAddress.String(),
		PayerAddress:           payerAddressStr,
		PayoutTargetAddress:    payoutTargetAddress,
		PaymentAddressSalt:     intent.PaymentAddressSalt,
		TokenAddress:           intent.TokenAddress,
		Symbol:                 intent.Symbol,
		Decimals:               intent.Decimals,
		CryptoAmount:           intent.CryptoAmount,
		ReceivedCryptoAmount:   intent.ReceivedCryptoAmount,
		AggregatedCryptoAmount: intent.AggregatedCryptoAmount,
		RefundCryptoAmount:     intent.RefundCryptoAmount,
		KGFeeAmount:            intent.KGFeeAmount,
		FiatAmount:             intent.FiatAmount,
		FiatCurrency:           intent.FiatCurrency,
		CryptoPrice:            intent.CryptoPrice,
		PricingMode:            intent.PricingMode,
		PaymentDeadline:        intent.PaymentDeadline,
		Status:                 intent.Status,
		PaymentTxHash:          intent.PaymentTxHash,
		PaymentTxTimestamp:     intent.PaymentTxTimestamp,
		AggregationTxHash:      intent.AggregationTxHash,
		AggregationTxTimestamp: intent.AggregationTxTimestamp,
		FinalizedTimestamp:     intent.FinalizedTimestamp,
		RefundTxHash:           intent.RefundTxHash,
		OrderData:              orderData,
		CallbackURL:            intent.CallbackURL,
		GroupKey:               intent.GroupKey,
	}

	if err := r.db.WithContext(ctx).Create(record).Error; err != nil {
		return nil, err
	}

	return toDomainPaymentIntent(record), nil
}

func (r *Repo) GetPaymentIntentByID(ctx context.Context, id string) (*domain.PaymentIntent, error) {
	var record model.PaymentIntent
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}

	return toDomainPaymentIntent(&record), nil
}

func (r *Repo) GetPaymentIntents(ctx context.Context, params domain.GetPaymentIntentsParams) ([]*domain.PaymentIntent, int, error) {
	var records []*model.PaymentIntent
	var totalCount int64

	baseQuery := r.db.WithContext(ctx).Model(&model.PaymentIntent{}).Where("org_id = ?", params.OrgID)
	if params.ClientID != nil {
		baseQuery = baseQuery.Where("client_id = ?", *params.ClientID)
	}

	// Handle status filtering
	if len(params.Status) > 0 {
		baseQuery = baseQuery.Where("status IN ?", params.Status)
	}

	// Handle scenario ID filtering
	if params.GroupKey != nil {
		baseQuery = baseQuery.Where("group_key LIKE ?", "%"+*params.GroupKey+"%")
	}

	// Handle chain ID filtering
	if params.ChainID != nil {
		baseQuery = baseQuery.Where("payment_chain = ?", *params.ChainID)
	}

	// Count total records
	if err := baseQuery.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := (params.Page - 1) * params.PageSize
	if err := baseQuery.
		Order("created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Find(&records).Error; err != nil {
		return nil, 0, err
	}

	result := make([]*domain.PaymentIntent, len(records))
	for i, record := range records {
		result[i] = toDomainPaymentIntent(record)
	}

	return result, int(totalCount), nil
}

func (r *Repo) GetPaymentIntentByChainAddress(ctx context.Context, ca domain.ChainAddress) (*domain.PaymentIntent, error) {
	var record model.PaymentIntent
	if err := r.db.WithContext(ctx).Where("payment_chain = ? AND payment_address = ?", ca.Chain.ID(), ca.Address.String()).First(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}
	return toDomainPaymentIntent(&record), nil
}

func (r *Repo) UpdatePaymentIntent(ctx context.Context, id string, update *domain.PaymentIntentUpdate) error {
	updates := map[string]any{}

	if update.Status != nil {
		updates["status"] = *update.Status
	}
	if update.PaymentTxHash != nil {
		updates["payment_tx_hash"] = *update.PaymentTxHash
	}
	if update.PaymentTxTimestamp != nil {
		updates["payment_tx_timestamp"] = *update.PaymentTxTimestamp
	}
	if update.AggregationTxHash != nil {
		updates["aggregation_tx_hash"] = *update.AggregationTxHash
	}
	if update.AggregationTxTimestamp != nil {
		updates["aggregation_tx_timestamp"] = *update.AggregationTxTimestamp
	}
	if update.FinalizedTimestamp != nil {
		updates["finalized_timestamp"] = *update.FinalizedTimestamp
	}
	if update.ReceivedCryptoAmount != nil {
		updates["received_amount"] = *update.ReceivedCryptoAmount
	}
	if update.AggregatedCryptoAmount != nil {
		updates["aggregated_amount"] = *update.AggregatedCryptoAmount
	}
	if update.RefundCryptoAmount != nil {
		updates["refund_amount"] = *update.RefundCryptoAmount
	}
	if update.RefundTxHash != nil {
		updates["refund_tx_hash"] = *update.RefundTxHash
	}
	if update.KGFeeAmount != nil {
		updates["kg_fee_amount"] = *update.KGFeeAmount
	}
	if update.PayerAddress != nil {
		updates["payer_address"] = (*update.PayerAddress).String()
	}

	if len(updates) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).
		Model(&model.PaymentIntent{}).
		Where("id = ?", id).
		Updates(updates)

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return domain.ErrRecordNotFound
	}

	return nil
}

func (r *Repo) GetPendingIntentsByPaymentAddresses(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]*domain.PaymentIntent, error) {
	addressesStr := lo.Map(addresses, func(address domain.Address, _ int) string {
		return address.String()
	})
	query := r.db.WithContext(ctx).
		Model(&model.PaymentIntent{}).
		Where("payment_address IN ?", addressesStr).
		Where("payment_chain = ?", chain.ID()).
		Where("status = ?", domain.PaymentIntentStatusPending)

	var paymentIntents []*model.PaymentIntent
	err := query.Find(&paymentIntents).Error
	if err != nil {
		return nil, err
	}

	return lo.SliceToMap(paymentIntents, func(intent *model.PaymentIntent) (domain.Address, *domain.PaymentIntent) {
		return domain.NewAddressByChain(chain, intent.PaymentAddress), toDomainPaymentIntent(intent)
	}), nil
}

func (r *Repo) GetPendingIntents(ctx context.Context) ([]*domain.PaymentIntent, error) {
	var paymentIntents []*model.PaymentIntent
	err := r.db.WithContext(ctx).Where("status = ?", domain.PaymentIntentStatusPending).Find(&paymentIntents).Error
	if err != nil {
		return nil, err
	}
	return lo.Map(paymentIntents, func(record *model.PaymentIntent, _ int) *domain.PaymentIntent {
		return toDomainPaymentIntent(record)
	}), nil
}

func toDomainPaymentIntent(record *model.PaymentIntent) *domain.PaymentIntent {
	chain := domain.IDToChain(record.PaymentChain)
	orderData := map[string]interface{}{}
	if record.OrderData != "" {
		err := json.Unmarshal([]byte(record.OrderData), &orderData)
		if err != nil {
			kglog.Errorf("failed to unmarshal order data: %v", err)
		}
	}

	var payerAddress domain.Address
	if record.PayerAddress != "" {
		payerAddress = domain.NewAddressByChain(chain, record.PayerAddress)
	}

,	var payoutTargetAddress domain.Address
	if record.PayoutTargetAddress != nil {
		payoutTargetAddress = domain.NewAddressByChain(chain, *record.PayoutTargetAddress)
	}

	return &domain.PaymentIntent{
		ID:                     record.ID,
		ClientID:               record.ClientID,
		OrgID:                  record.OrgID,
		PaymentChain:           chain,
		PaymentAddress:         domain.NewAddressByChain(chain, record.PaymentAddress),
		PayerAddress:           payerAddress,
		PayoutTargetAddress:    payoutTargetAddress,
		PaymentAddressSalt:     record.PaymentAddressSalt,
		TokenAddress:           record.TokenAddress,
		Symbol:                 record.Symbol,
		Decimals:               record.Decimals,
		CryptoAmount:           record.CryptoAmount,
		ReceivedCryptoAmount:   record.ReceivedCryptoAmount,
		AggregatedCryptoAmount: record.AggregatedCryptoAmount,
		RefundCryptoAmount:     record.RefundCryptoAmount,
		KGFeeAmount:            record.KGFeeAmount,
		FiatAmount:             record.FiatAmount,
		FiatCurrency:           record.FiatCurrency,
		CryptoPrice:            record.CryptoPrice,
		PricingMode:            record.PricingMode,
		PaymentDeadline:        record.PaymentDeadline,
		Status:                 record.Status,
		PaymentTxHash:          record.PaymentTxHash,
		PaymentTxTimestamp:     record.PaymentTxTimestamp,
		AggregationTxHash:      record.AggregationTxHash,
		AggregationTxTimestamp: record.AggregationTxTimestamp,
		FinalizedTimestamp:     record.FinalizedTimestamp,
		RefundTxHash:           record.RefundTxHash,
		OrderData:              orderData,
		CallbackURL:            record.CallbackURL,
		GroupKey:               record.GroupKey,
	}
}
